<template>
  <div
    v-loading="loading"
    class="system-customer main">
    <xr-header
      label="邮件账号" />
       <div class="main-body">
    <flexbox class="handle-bar" justify="space-between">
      <flexbox>
        <!-- 人员搜索选择器 -->
        <el-select
          v-model="selectedUser"
          filterable
          remote
          reserve-keyword
          placeholder="请搜索人员"
          :remote-method="remoteSearchUser"
          :loading="userSearchLoading"
          clearable
          class="select-item">
          <el-option
            v-for="item in userSearchOptions"
            :key="item.userId"
            :label="item.realname"
            :value="item.userId">
            <span style="float: left">{{ item.realname }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.deptName || '无部门' }}</span>
          </el-option>
          <el-button
            slot="suffix"
            type="icon"
            icon="wk wk-sousuo"
            @click.native="handleUserSearch" />
        </el-select>
        <el-select
          v-model="typeSelect"
          placeholder="请选择账号类型"
          class="select-item">
          <el-option
            v-for="item in [{label:'个人', value:1},{label:'公共', value:2}]"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select
          v-model="protocolSelect"
          placeholder="请选择邮箱协议"
          class="select-item">
          <el-option
            v-for="item in [{label:'pop', value:'pop'},{label:'imap', value:'imap'}]"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-input
          v-model="searchInput"
          placeholder="请输入邮箱名"
          class="search-input"
          @keyup.enter.native="headerSearch"
          @blur="headerSearch">
          <el-button
            slot="suffix"
            type="icon"
            icon="wk wk-sousuo"
            @click.native="headerSearch" />
        </el-input>
        <el-button
          type="primary"
          @click="handleClick('search')">搜索</el-button>
      </flexbox>

      <div
        style="flex-shrink: 0;">
        <el-button
        type="primary"
          @click="addViewShow = true">添加邮箱</el-button>
      </div>
    </flexbox>
    <div class="content">
      <el-table
        id="task-set-table"
        :data="list"
        :class="WKConfig.tableStyle.class"
        :stripe="WKConfig.tableStyle.stripe"
        :height="tableHeight"
        :cell-style="cellStyle"
        header-align="center"
        align="center"
        highlight-current-row>
        <el-table-column
          v-for="(item, index) in fieldList"
          :key="index"
          :fixed="index==0"
          :prop="item.field"
          :label="item.name"
          show-overflow-tooltip
          width="150">
          <template slot-scope="scope">
            <div
              v-if="item.field === 'name' && scope.$index!==list.length -1 "
              class="table-show-item"><i
                v-if="isEdit"
                :class="{ 'is-show': isEdit }"
                class="wk wk-delete"
                @click="deleteAchievement(scope.row, scope.$index)" />{{ scope.row[item.field] }}
            </div>
            <div
              v-else-if="item.field === 'name'
                || item.field === 'yeartarget'
                || item.field === 'first'
                || item.field === 'second'
                || item.field === 'third'
                || item.field === 'fourth' || !isEdit || ( scope.$index===list.length -1 )"
              class="table-show-item">
              <template v-if="index === 0">
                {{ scope.row[item.field] }}
              </template>
              <template v-else>
                {{ scope.row[item.field] | separator }}
              </template>
            </div>
            <el-input
              v-else
              v-model="scope.row[item.field]"
              type="number"
              @input="handleInputEdit('change', scope)"
              @blur="handleInputEdit('blur', scope)" />
          </template>
        </el-table-column>
      </el-table>
    </div>
</div>
    <add-goal
      :visible.sync="addViewShow"
      :type="tabType"
      @success="tabTypeClick" />
    <!-- 导入目标 -->
  </div>
</template>

<script>
import { depListAPI, userListAPI } from '@/api/common'
import {
  crmAchievementIndex,
  crmAchievementUpdate,
  crmAchievementDelete
} from '@/api/admin/crm'
import XrHeader from '@/components/XrHeader'
import AddGoal from './components/AddGoal'
// import ImportGoal from './ImportGoal'
import WkDeptDialogSelect from '@/components/NewCom/WkDeptDialogSelect'
import WkUserDialogSelect from '@/components/NewCom/WkUserDialogSelect'

import moment from 'moment'
import { exportElTable } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  /** 业绩目标设置 */
  name: 'TaskSetStatistics',
  components: {
    AddGoal,
    // ImportGoal,
    WkDeptDialogSelect,
    WkUserDialogSelect,
    XrHeader
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      loading: false,
      tableHeight: document.documentElement.clientHeight - 280,
      tabType: 'department',

      dateSelect: '', // 选择的date区间
      typeSelect: '', // 类型选择 1销售（目标）2回款（目标）
      protocolSelect: '', // 邮箱协议选择
      /** 部门选择解析数据 */
      structuresProps: {
        children: 'children',
        label: 'label',
        value: 'id'
      },
      deptList: [], // 部门列表
      structuresSelectValue: '',
      /** 用户列表 */
      userOptions: [],
      userSelectValue: '',
      /** 人员搜索相关 */
      selectedUser: '', // 选中的用户ID
      userSearchOptions: [], // 搜索到的用户列表
      userSearchLoading: false, // 搜索加载状态
      defaultUserOptions: [], // 缓存的默认用户选项
      searchInput: '', // 邮箱搜索输入框内容

      /** 编辑控制 */
      isEdit: false, // 是否是编辑中

      list: [],
      fieldList: [
        { field: 'name', name: '邮箱账号' },
        { field: 'yeartarget', name: '拥有人' },
        { field: 'first', name: '账号类型' },
        { field: 'january', name: '邮箱协议' },
        { field: 'february', name: '操作' }
      ],

      // 设置目标
      addViewShow: false,
      // 导入目标
      importGoalShow: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    window.onresize = () => {
      this.tableHeight = document.documentElement.clientHeight - 280
    }

    this.dateSelect = moment()
      .year()
      .toString()
    this.getDeptList()
    this.initUserSearchOptions()
  },
  methods: {
     headerSearch() {
    //   this.refreshUserList()
    },

    /**
     * 远程搜索用户
     */
    remoteSearchUser(query) {
      if (query !== '') {
        this.userSearchLoading = true
        userListAPI({
          pageType: 0,
          realname: query
        }).then(res => {
          this.userSearchOptions = res.data.list || []
          this.userSearchLoading = false
        }).catch(() => {
          this.userSearchLoading = false
        })
      } else {
        // 当搜索内容为空时，恢复默认选项
        this.userSearchOptions = [...this.defaultUserOptions]
      }
    },

    /**
     * 用户选择变化
     */
    handleUserChange(userId) {
      if (userId) {
        const selectedUserInfo = this.userSearchOptions.find(user => user.userId === userId)
        console.log('选中用户:', selectedUserInfo)
        // 这里可以根据需要处理用户选择后的逻辑
        // 比如筛选邮箱账号列表等
      } else {
        console.log('清空用户选择')
        // 清空选择的处理逻辑
      }
    },

    /**
     * 用户搜索按钮点击
     */
    handleUserSearch() {
      console.log('点击用户搜索按钮')
      // 这里可以添加搜索按钮的处理逻辑
      // 比如触发搜索或者其他操作
    },

    /**
     * 初始化用户搜索选项
     */
    initUserSearchOptions() {
      // 如果已经有缓存的默认选项，直接使用
      if (this.defaultUserOptions.length > 0) {
        this.userSearchOptions = [...this.defaultUserOptions]
        return
      }

      // 添加一些示例数据，与截图中的数据保持一致
      const defaultOptions = [
        { userId: 1, realname: '全公司', deptName: '管理部门' },
        { userId: 2, realname: 'Sydney Tang', deptName: '技术部' },
        { userId: 3, realname: '罗文欣', deptName: '销售部' },
        { userId: 4, realname: '邓娟丹', deptName: '市场部' },
        { userId: 5, realname: '钟秋玲', deptName: '人事部' }
      ]

      // 设置默认选项
      this.defaultUserOptions = [...defaultOptions]
      this.userSearchOptions = [...defaultOptions]

      // 同时从API获取真实数据
      userListAPI({
        pageType: 0,
        limit: 20 // 获取前20个用户作为默认选项
      }).then(res => {
        if (res.data.list && res.data.list.length > 0) {
          this.defaultUserOptions = res.data.list
          this.userSearchOptions = res.data.list
        }
      }).catch(() => {
        // 如果API调用失败，保持示例数据
        console.log('使用示例数据')
      })
    },
    tabTypeClick() {
      this.isEdit = false
      if (this.tabType === 'department') {
        this.getAhievementList()
      } else if (this.tabType === 'user') {
        this.getUserList() // 更新员工列表
        this.getAhievementListForUser()
      }
    },
    /** 获取部门业绩目标列表 */
    getAhievementList() {
      this.loading = true
      crmAchievementIndex({
        year: this.dateSelect,
        type: 2, // 2部门3员工
        status: this.typeSelect,
        deptId: this.structuresSelectValue
      })
        .then(res => {
          this.list = res.data.map(item => {
            item.name = item.objName
            return this.getShowItem(item)
          })
          if (this.list.length) {
            this.getSubTotalModel()
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getShowItem(item) {
      item['first'] = this.calculateFirst(item)
      item['second'] = this.calculateSecond(item)
      item['third'] = this.calculateThird(item)
      item['fourth'] = this.calculateFourth(item)
      item['yeartarget'] = this.calculateAll(item)
      return item
    },
    /** 获取直属下级目标总和 */
    getSubTotalModel() {
      var initModel = {
        name: '目标合计',
        january: '0.00',
        february: '0.00',
        march: '0.00',
        april: '0.00',
        may: '0.00',
        june: '0.00',
        july: '0.00',
        august: '0.00',
        september: '0.00',
        october: '0.00',
        november: '0.00',
        december: '0.00',
        yeartarget: '0.00',
        first: '0.00',
        second: '0.00',
        third: '0.00',
        fourth: '0.00',
        ignore: true
      }

      // 从下属数据1 开始循环
      for (let index = 0; index < this.list.length; index++) {
        const element = this.list[index]
        for (
          let fieldIndex = 0;
          fieldIndex < this.fieldList.length;
          fieldIndex++
        ) {
          const fieldItem = this.fieldList[fieldIndex]
          if (fieldItem.field !== 'name') {
            initModel[fieldItem.field] = (
              parseFloat(initModel[fieldItem.field]) +
              parseFloat(element[fieldItem.field])
            )
              .toFixed(2)
              .toString()
          }
        }
      }

      this.list.push(initModel)
      // this.list.splice(1, 0, initModel)
    },
    handleInputEdit(type, scope) {
      if (type === 'change') {
        var value = scope.row[scope.column.property]
          ? scope.row[scope.column.property]
          : '0.00'
        var newValue = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
        if (value !== newValue) {
          scope.row[scope.column.property] = newValue
          this.$set(this.list, scope.$index, scope.row)
        }
      } else if (type === 'blur') {
        /** 一季度 */
        if (
          scope.column.property === 'january' ||
          scope.column.property === 'february' ||
          scope.column.property === 'march'
        ) {
          scope.row['first'] = this.calculateFirst(scope.row)
          scope.row['yeartarget'] = this.calculateAll(scope.row)
        } else if (
          scope.column.property === 'april' ||
          scope.column.property === 'may' ||
          scope.column.property === 'june'
        ) {
          scope.row['second'] = this.calculateSecond(scope.row)
          scope.row['yeartarget'] = this.calculateAll(scope.row)
        } else if (
          scope.column.property === 'july' ||
          scope.column.property === 'august' ||
          scope.column.property === 'september'
        ) {
          scope.row['third'] = this.calculateThird(scope.row)
          scope.row['yeartarget'] = this.calculateAll(scope.row)
        } else if (
          scope.column.property === 'october' ||
          scope.column.property === 'november' ||
          scope.column.property === 'december'
        ) {
          scope.row['fourth'] = this.calculateFourth(scope.row)
          scope.row['yeartarget'] = this.calculateAll(scope.row)
        }

        if (this.tabType === 'department' && this.list.length >= 2) {
          // 部门下 有子部门的时候 计算其值
          this.calculateSubTotal(scope)
        }
      }
    },
    /** 计算下属目标综合 */
    calculateSubTotal(scope) {
      var item = this.list[this.list.length - 1]
      var subValue = '0'
      for (let index = 0; index < this.list.length - 1; index++) {
        const element = this.list[index]
        subValue = (
          parseFloat(subValue) + parseFloat(element[scope.column.property])
        )
          .toFixed(2)
          .toString()
      }
      item[scope.column.property] = subValue

      /** 一季度 */
      if (
        scope.column.property === 'january' ||
        scope.column.property === 'february' ||
        scope.column.property === 'march'
      ) {
        item['first'] = this.calculateFirst(item)
        item['yeartarget'] = this.calculateAll(item)
      } else if (
        scope.column.property === 'april' ||
        scope.column.property === 'may' ||
        scope.column.property === 'june'
      ) {
        item['second'] = this.calculateSecond(item)
        item['yeartarget'] = this.calculateAll(item)
      } else if (
        scope.column.property === 'july' ||
        scope.column.property === 'august' ||
        scope.column.property === 'september'
      ) {
        item['third'] = this.calculateThird(item)
        item['yeartarget'] = this.calculateAll(item)
      } else if (
        scope.column.property === 'october' ||
        scope.column.property === 'november' ||
        scope.column.property === 'december'
      ) {
        item['fourth'] = this.calculateFourth(item)
        item['yeartarget'] = this.calculateAll(item)
      }
    },
    /**
     * 获取部门列表
     */
    getDeptList() {
      depListAPI({ type: 'tree' }).then(res => {
        this.deptList = res.data
        if (res.data.length > 0) {
          this.structuresSelectValue = res.data[0].id
          this.tabTypeClick() // 获取信息
        }
      })
    },
    /** 部门更改 */
    structuresValueChange() {
      console.log('this.structuresSelectValue--', this.structuresSelectValue)
      if (this.tabType === 'department') {
        if (this.userSelectValue) {
          // 在部门目标设置下更新部门 清空员工下的员工列表信息
          this.userSelectValue = ''
          this.userOptions = null
        }

        this.getUserList() // 更新员工列表
      } else if (this.tabType === 'user') {
        this.userSelectValue = ''
        this.userOptions = null
        this.getUserList() // 更新员工列表
      }
    },
    /** 部门下员工 */
    getUserList() {
      var params = { pageType: 0 }
      if (this.structuresSelectValue) {
        params.deptId = this.structuresSelectValue
        userListAPI(params)
          .then(res => {
            this.userOptions = res.data.list
          })
          .catch(() => {})
      } else {
        this.userSelectValue = ''
        this.userOptions = null
      }
    },
    /** 获取员工业绩目标列表 */
    getAhievementListForUser() {
      this.loading = true
      var id = this.structuresSelectValue
      crmAchievementIndex({
        year: this.dateSelect,
        type: 3, // 2部门3员工
        status: this.typeSelect,
        deptId: id,
        userId: this.userSelectValue
      })
        .then(res => {
          this.list = res.data.map(item => {
            item.name = item.objName
            return this.getShowItem(item)
          })
          if (this.list.length) {
            this.getSubTotalModel()
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    /** 顶部操作 */
    handleClick(type) {
      if (type == 'search') {
        this.updateAhievementList()
      } else if (type == 'edit') {
        this.isEdit = true
      } else if (type == 'import') {
        this.importGoalShow = true
      } else if (type == 'export') {
        const typeName = { department: '部门目标', user: '员工目标' }[this.tabType]
        const name = `${this.dateSelect} 年${typeName}.xlsx`
        exportElTable(name, 'task-set-table')
      } else if (type == 'save') {
        this.loading = true
        var list = this.list.filter(function(item, index, array) {
          return !item.ignore
        })
        crmAchievementUpdate(list)
          .then(res => {
            this.$message.success('操作成功')
            this.loading = false
            this.isEdit = false
            this.updateAhievementList()
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type == 'cancel') {
        this.updateAhievementList()
        this.isEdit = false
      }
    },

    /** 点击搜索 保存 取消时更新信息 */
    updateAhievementList() {
      if (this.tabType === 'department') {
        this.getAhievementList()
      } else if (this.tabType === 'user') {
        this.getAhievementListForUser()
      }
    },
    /** 通过回调控制style */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.list.length - 1) {
        return {
          backgroundColor: '#FAF9F6'
        }
      } else if (
        columnIndex == 1 ||
        columnIndex == 2 ||
        columnIndex == 6 ||
        columnIndex == 10 ||
        columnIndex == 14
      ) {
        return {
          backgroundColor: '#E5F4FE',
          textAlign: 'center'
        }
      } else {
        return { textAlign: 'center' }
      }
    },

    /**
     * 删除目标
     */
    deleteAchievement(data, index) {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          crmAchievementDelete({ achievementId: data.achievementId })
            .then(res => {
              this.loading = false
              this.$message.success('操作成功')

              this.list.splice(index, 1)
              this.list.pop()
              if (this.list.length) {
                this.getSubTotalModel()
              }
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => { })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../styles/index.scss";
.main-body {
  margin-top: #{$--interval-base * 2};
}
.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-x: auto;
}

.handle-bar {
  .el-date-editor {
    width: 130px;
    margin-right: 15px;
  }

  // 统一三个选择器的宽度
  .select-item {
    width: 160px;
    margin-right: 15px;
  }

  .wk-dept-dialog-select,
  .wk-user-dialog-select {
    width: 180px;
    margin-right: 15px;
  }

  .search-input {
    width: 200px;
    margin-right: 15px;
  }
}

.content {
  flex: 1;
  padding: 10px 0;
  overflow-y: hidden;
}

.el-tabs ::v-deep .el-tabs__nav-wrap::after {
  display: none !important;
}

.el-table ::v-deep th {
  text-align: center;
}

.table-show-item {
  height: 34px;
  line-height: 34px;
  text-align: center;
}

.wk-delete {
  cursor: pointer;
  opacity: 0;
}

.wk-delete.is-show {
  opacity: 1;
}

.wk-delete:hover {
  color: $--color-danger;
}
</style>
